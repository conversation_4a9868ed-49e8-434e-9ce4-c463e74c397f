"use client";

import { useEffect, useState } from "react";
import { authClient } from "@/lib/auth-client";
import type { auth } from "@/lib/auth";

export const useSession = () => {
  const [session, setSession] = useState<typeof auth.$Infer.Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getSession = async () => {
      try {
        setLoading(true);
        const { data, error } = await authClient.getSession();
        if (error) {
          setError(error.message || "Failed to get session");
          setSession(null);
        } else {
          setSession(data);
          setError(null);
        }
      } catch (err: any) {
        setError(err.message || "Failed to get session");
        setSession(null);
      } finally {
        setLoading(false);
      }
    };

    getSession();
  }, []);

  const refreshSession = async () => {
    try {
      setLoading(true);
      const { data, error } = await authClient.getSession();

      if (error) {
        setError(error.message || "Failed to refresh session");
        setSession(null);
      } else {
        setSession(data);
        setError(null);
      }
    } catch (err: any) {
      setError(err.message || "Failed to refresh session");
      setSession(null);
    } finally {
      setLoading(false);
    }
  };

  return {
    session,
    user: session?.user || null,
    loading,
    error,
    refreshSession,
  };
};
